package com.interfun.buz.home.view.block

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.collectInScope
import com.interfun.buz.base.ktx.collectLatestIn
import com.interfun.buz.base.ktx.requireContext
import com.interfun.buz.biz.center.voicefilter.tracker.VoiceFilterTracker
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.RecordPermissionCheckEnhance
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.ui.RecordCountDownScreen
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction.*
import com.interfun.buz.domain.im.social.entity.isGroup
import com.interfun.buz.domain.record.ui.CombineRecordScreen
import com.interfun.buz.domain.record.viewmodel.RecordVoiceViewModel
import com.interfun.buz.domain.record.viewmodel.VoiceFilterViewModel
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.interfun.buz.home.view.compose.VoiceTestButton
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.IMSendStateEvent
import com.interfun.buz.im.entity.NoVoiceFilterCode
import com.interfun.buz.im.entity.VoiceFilterAIErrorType

/**
 * <AUTHOR>
 * @date 2024/12/2
 * @desc
 */
class HomeRecordUiBlock(
    val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding,
) : BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {

    companion object {
        const val TAG = "HomeRecordUiBlock"
    }

    private val recordViewModel by fragment.viewModels<RecordVoiceViewModel>()
    private val voiceFilterViewModel by fragment.viewModels<VoiceFilterViewModel>()
    private val recordAnimHelper = HomeRecordAnimHelper(fragment)

    private var voiceFilterExposureTime = 0L
    private val recordPermissionCheckEnhance by lazy {
        RecordPermissionCheckEnhance(
            fragment.requireActivity(),
            fragment.parentFragmentManager,
            "${TAG}_RecordPermissionCheckEnhance",
            denyToastCopy = fragment.requireContext.getString(R.string.system_access_mic_for_sending)
        )
    }

    override fun initView() {
        super.initView()
        initComposeView()
        recordAnimHelper.initView()
    }

    private fun initComposeView() {
        fragment.bindingBottom.cvHomeBottom.setContent {
            val isRvWTScrolling by WTStatusManager.isRvWTScrollingFlow.collectAsStateWithLifecycle()
            Column {
                // 测试按钮
                VoiceTestButton(
                    onTestVoiceClick = { fragment.onTestVoiceButtonClick() }
                )

                // 原有的录音界面
                CombineRecordScreen(
                    onActions = getRecordActions(),
                    isInterceptGestureLambda = { isRvWTScrolling }
                )
            }
        }
        fragment.bindingCenter.cvRecordingCountDown.setContent {
            val isRecording by recordViewModel.isRecordingFlow.collectAsStateWithLifecycle()
            val isLocking by recordViewModel.isLockingFlow.collectAsStateWithLifecycle(false)
            val currentPressArea by recordViewModel.currentPressAreaFlow.collectAsStateWithLifecycle()
            val isInVoiceFilterMode by voiceFilterViewModel.isVoiceFilterModeStateFlow.collectAsStateWithLifecycle()
            val voiceFilter by voiceFilterViewModel.currentSelectVoiceFilterUiDataFlow.collectAsStateWithLifecycle()
            RecordCountDownScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 20.dp),
                centerSpaceHeight = 4.dp,
                showReleaseToSendHint = true,
                cancelHint = R.string.home_release_to_cancel_recording.asString(),
                previewHint = R.string.release_to_preview_recording.asString(),
                lockHint = R.string.release_to_record_hands_free.asString(),
                hintTextStyle = TextStyles.bodyLarge(),
                isRecordingLambda = { isRecording },
                isLockingLambda = { isLocking },
                currentPressAreaLambda = { currentPressArea },
                voiceFilterLambda = {
                    voiceFilter.takeIf {
                        isInVoiceFilterMode && it?.filterId != VoiceFilterUiData.NO_FILTER_ID
                    }
                }
            )
        }
    }

    private fun getRecordActions(): (RecordScreenAction) -> Unit = { action ->
        when (action) {
            OnSelectVoiceExposure -> {
                VoiceFilterTracker.onViewVoiceFilter(
                    isGroup = WTStatusManager.homeCurrentSelectedConversationValue?.isGroup ?: false,
                    pageType = "home"
                )
                voiceFilterExposureTime = System.currentTimeMillis()
            }

            is OnClickClear -> {
                VoiceFilterTracker.onClickVoiceFilterBtn(
                    isGroup = WTStatusManager.homeCurrentSelectedConversationValue?.isGroup ?: false,
                    targetId = WTStatusManager.homeCurrentSelectedConversationValue?.convId ?: 0L,
                    type = VoiceFilterTracker.TYPE_CLEAR,
                    filterId = action.voiceFilterId ?: VoiceFilterUiData.NO_FILTER_ID,
                    pageType = "homepage"
                )
            }

            OnClickPreviewVoiceFilter -> {
                VoiceFilterTracker.onClickVoiceFilterBtn(
                    isGroup = WTStatusManager.homeCurrentSelectedConversationValue?.isGroup ?: false,
                    targetId = WTStatusManager.homeCurrentSelectedConversationValue?.convId ?: 0L,
                    type = VoiceFilterTracker.TYPE_PREVIEW_FILTER,
                    filterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId(),
                    pageType = "homepage"
                )
            }

            OnClickQuit -> {
                val stayTime = System.currentTimeMillis() - voiceFilterExposureTime
                VoiceFilterTracker.onClickVoiceFilterBtn(
                    isGroup = WTStatusManager.homeCurrentSelectedConversationValue?.isGroup ?: false,
                    targetId = WTStatusManager.homeCurrentSelectedConversationValue?.convId ?: 0L,
                    type = VoiceFilterTracker.TYPE_QUIT,
                    filterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId(),
                    pageType = "homepage",
                    quitTime = stayTime
                )
            }

            OnRequestRecordPermission -> {
                recordPermissionCheckEnhance.checkPermission {
                    CommonTracker.postAudioPermissionCheckResult(
                        if (it) "enable" else "disable",
                        isUserBehaviour = true
                    )
                }
            }

            is OnSelectVoiceFilterTab -> {
                VoiceFilterTracker.onVoiceFilterTabClick(
                    isGroup = WTStatusManager.homeCurrentSelectedConversationValue?.isGroup?: false,
                    convId = (WTStatusManager.homeCurrentSelectedConversationValue?.convId?: 0L).toString(),
                    source = "homepage",
                    tabType = voiceFilterViewModel.getCurrentSelectTabType().toString(),
                )
            }

            is OnClickCampaignEntry -> {
                VoiceFilterTracker.onVoiceFilterCampaignEntryClick(
                    isGroup = WTStatusManager.homeCurrentSelectedConversationValue?.isGroup?: false,
                    convId = (WTStatusManager.homeCurrentSelectedConversationValue?.convId?: 0L).toString(),
                    source = "homepage",
                    voiceFilterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId().toString(),
                    campaignId = (voiceFilterViewModel.getCurrentSelectVoiceFilterUiData().campaignConfig?.id)?.toString(),
                )
            }

            else -> {}
        }
    }

    override fun initData() {
        super.initData()
        voiceFilterViewModel.isVoiceFilterModeStateFlow.collectLatestIn(fragment.viewLifecycleOwner) {
            recordAnimHelper.animBottomBtnWhenVFModeChange(isShow = !it)
        }
        recordViewModel.isRecordingFlow.collectLatestIn(fragment.viewLifecycleOwner) {
            if (it) {
                recordAnimHelper.onStartRecord()
            } else {
                recordAnimHelper.onStopRecord()
            }
        }
        // 监听 IM 消息，处理语音滤镜相关的错误
        IMAgent.msgVoiceFilterFlow.collectInScope(voiceFilterViewModel.viewModelScope) {
            if (it.error?.errorType == VoiceFilterAIErrorType && it.error?.errorCode == NoVoiceFilterCode) {
                voiceFilterViewModel.syncFromServer()
            }
        }
        IMAgent.msgSendStateFlow.collectInScope(voiceFilterViewModel.viewModelScope) { imSendState ->
            if (imSendState is IMSendStateEvent.OnError && imSendState.errorType == VoiceFilterAIErrorType && imSendState.errorCode == NoVoiceFilterCode) {
                voiceFilterViewModel.syncFromServer()
            }
        }
    }

}