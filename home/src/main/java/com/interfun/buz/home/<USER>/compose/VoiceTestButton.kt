package com.interfun.buz.home.view.compose

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.components.CommonButton
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.InitPreview

/**
 * 语音消息测试按钮组件
 * 用于开发者快速测试语音消息发送功能
 * 
 * @param modifier 修饰符
 * @param onTestVoiceClick 测试按钮点击回调
 * @param isEnabled 按钮是否可用
 */
@Composable
fun VoiceTestButton(
    modifier: Modifier = Modifier,
    onTestVoiceClick: () -> Unit = {},
    isEnabled: Boolean = true
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        CommonButton(
            modifier = Modifier.wrapContentWidth(),
            type = CommonButtonType.TERTIARY_SMALL,
            text = "🎤 测试语音消息",
            enable = isEnabled,
            onClick = onTestVoiceClick
        )
    }
}

/**
 * 预览组件
 */
@Preview(showBackground = true)
@Composable
private fun VoiceTestButtonPreview() {
    InitPreview()
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 正常状态
        VoiceTestButton(
            onTestVoiceClick = { /* 预览中的点击事件 */ },
            isEnabled = true
        )
        
        // 禁用状态
        VoiceTestButton(
            onTestVoiceClick = { /* 预览中的点击事件 */ },
            isEnabled = false
        )
    }
}
