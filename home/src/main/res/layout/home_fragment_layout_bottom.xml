<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/home_conv_list_bg"
    tools:layout_marginTop="400dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceBottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceCenter"
        tools:layout_constraintTop_toTopOf="parent" />

    <!-- 首页底部按钮空间: height = button size (60) + home layout padding (20) -->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceBottomButtons"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 注意：emoji弹窗的高度会基于这个space-->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceVEPanel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/home_dialog_panel_gap_with_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceWTRvMarginBottom"
        app:layout_constraintBottom_toTopOf="@+id/spaceBottomButtons"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clRecordArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/spaceBottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceBottom">

        <View
            android:id="@+id/viewRecordArea"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.53"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.47" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/cvHomeBottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceBottom"
        app:layout_constraintEnd_toEndOf="@+id/spaceBottom"
        app:layout_constraintStart_toStartOf="@+id/spaceBottom"
        app:layout_constraintTop_toTopOf="@+id/spaceBottom" />

    <!-- 点击群@机器人外缩小 -->
    <View
        android:id="@+id/vAddressAiMask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="gone" />

    <!-- 群@机器人布局 -->
    <com.interfun.buz.home.view.widget.WTGroupAddressAiView
        android:id="@+id/vGroupAiSelector"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/home_address_ai_parent_expanded_width"
        android:layout_marginBottom="20dp"
        android:background="@color/color_background_4_default"
        app:layout_constraintBottom_toBottomOf="@+id/spaceBottomButtons"
        app:layout_constraintStart_toStartOf="@+id/spaceBottomButtons"
        app:layout_constraintEnd_toEndOf="@+id/spaceBottomButtons"
        app:round_radius="@dimen/home_address_ai_bg_radius_min"
        tools:layout_height="@dimen/home_address_ai_bg_collapsed_height"
        tools:layout_width="100dp"
        tools:visibility="visible" />

    <!--左侧VE按钮-->
    <FrameLayout
        android:id="@+id/flLeftBtnVoiceMoji"
        android:layout_width="@dimen/home_more_buttons_size"
        android:layout_height="@dimen/home_more_buttons_size"
        android:layout_marginStart="@dimen/home_layout_padding"
        android:layout_marginBottom="@dimen/home_layout_padding"
        android:background="@drawable/common_oval_background_4"
        app:layout_constraintTop_toTopOf="@+id/spaceBottomButtons"
        app:layout_constraintStart_toStartOf="@+id/spaceBottomButtons"
        tools:ignore="SpUsage">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvVoiceMoji"
            style="@style/iconfont_base"
            android:layout_width="@dimen/home_more_buttons_size"
            android:layout_height="@dimen/home_more_buttons_size"
            android:background="@drawable/home_bottom_btn_bg_selector_drawable"
            android:gravity="center"
            android:text="@string/ic_voice_moji"
            android:textColor="@color/home_bottom_btn_icon_selector_color"
            android:textSize="24dp" />

        <!--左侧语音按钮：面板打开时转换成X-->
        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvCancelVoiceMoji"
            style="@style/iconfont_base"
            android:layout_width="@dimen/home_more_buttons_size"
            android:layout_height="@dimen/home_more_buttons_size"
            android:gravity="center"
            android:text="@string/ic_add"
            android:textColor="@color/text_white_main"
            android:rotation="45"
            android:textSize="24dp"
            android:scaleX="0"
            android:scaleY="0"
            tools:visibility="visible"/>

        <!--盲盒提示-->
        <View
            android:id="@+id/vBlindBoxReminder"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:background="@drawable/home_blind_box_reminder_oval_bg"
            android:visibility="gone"
            android:layout_marginTop="-3dp"
            android:layout_marginEnd="-3dp"
            android:layout_gravity="end|top"
            tools:visibility="visible" />

    </FrameLayout>

    <!--底部面板打开时的背景蒙层（部分 View 需要透出）-->
    <View
        android:id="@+id/viewPanelMask"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="-20dp"
        android:alpha="0"
        android:background="@color/color_overlay_black_medium"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <!--右侧加号按钮-->
    <FrameLayout
        android:id="@+id/flRightBtnMore"
        android:layout_width="@dimen/home_more_buttons_size"
        android:layout_height="@dimen/home_more_buttons_size"
        android:layout_marginStart="@dimen/home_layout_padding"
        android:layout_marginEnd="@dimen/home_layout_padding"
        android:layout_marginBottom="@dimen/home_layout_padding"
        android:background="@drawable/common_oval_background_4"
        app:layout_constraintEnd_toEndOf="@+id/spaceBottomButtons"
        app:layout_constraintTop_toTopOf="@+id/spaceBottomButtons"
        tools:ignore="SpUsage">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvMore"
            style="@style/iconfont_base"
            android:layout_width="@dimen/home_more_buttons_size"
            android:layout_height="@dimen/home_more_buttons_size"
            android:background="@drawable/home_bottom_btn_bg_selector_drawable"
            android:gravity="center"
            android:rotation="0"
            android:text="@string/ic_add"
            android:textColor="@color/home_bottom_btn_icon_selector_color"
            android:textSize="24dp"
            tools:visibility="visible" />

        <View
            android:id="@+id/vMoreReminder"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="end|top"
            android:layout_marginTop="-3dp"
            android:layout_marginEnd="-3dp"
            android:background="@drawable/home_blind_box_reminder_oval_bg"
            android:visibility="gone"
            tools:visibility="visible" />

    </FrameLayout>

    <!-- 测试发送语音消息按钮 - 仅用于开发测试 -->
    <Button
        android:id="@+id/btnTestSendVoice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:text="Test Voice"
        android:textSize="10sp"
        android:padding="8dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:background="@android:color/holo_blue_light"
        android:textColor="@android:color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible" />

</merge>